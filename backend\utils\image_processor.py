"""
图片处理工具模块
"""

import os
import logging
from pathlib import Path
from fastapi import UploadFile, HTTPException
from PIL import Image
from config import config

logger = logging.getLogger(__name__)


class ImageProcessor:
    """图片处理类"""
    
    @staticmethod
    def validate_image(file: UploadFile) -> bool:
        """验证上传的图片文件"""
        # 检查文件大小
        if file.size > config.MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="文件大小超过限制")
        
        # 检查文件扩展名
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in config.ALLOWED_EXTENSIONS:
            raise HTTPException(status_code=400, detail="不支持的文件格式")
        
        return True
    
    @staticmethod
    def preprocess_image(image_path: str) -> str:
        """预处理图片，提高OCR识别率"""
        try:
            with Image.open(image_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 如果图片太大，适当缩放
                max_size = 2048
                if img.width > max_size or img.height > max_size:
                    img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
                
                # 保存处理后的图片
                processed_path = image_path.replace('.', '_processed.')
                img.save(processed_path, 'JPEG', quality=95)
                
                return processed_path
        except Exception as e:
            logger.error(f"图片预处理失败: {str(e)}")
            return image_path
