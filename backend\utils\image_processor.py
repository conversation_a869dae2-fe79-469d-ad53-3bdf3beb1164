"""
图片处理工具模块
"""

import os
import logging
from pathlib import Path
from fastapi import UploadFile, HTTPException
from PIL import Image
import cv2
import numpy as np
from config import config

logger = logging.getLogger(__name__)


class ImageProcessor:
    """图片处理类"""
    
    @staticmethod
    def validate_image(file: UploadFile) -> bool:
        """验证上传的图片文件"""
        # 检查文件大小
        if file.size > config.MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="文件大小超过限制")
        
        # 检查文件扩展名
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in config.ALLOWED_EXTENSIONS:
            raise HTTPException(status_code=400, detail="不支持的文件格式")
        
        return True
    
    @staticmethod
    def detect_and_crop_popup(image_path: str) -> str:
        """
        检测并裁剪弹窗区域
        """
        img = cv2.imread(image_path)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 第一次阈值，分离弹窗+阴影
        _, thresh1 = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)

        # 找最大轮廓（弹窗+阴影）
        contours, _ = cv2.findContours(255-thresh1, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        max_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(max_contour)

        # 在弹窗区域内，二次阈值，提取真正的白色弹窗
        roi = gray[y:y+h, x:x+w]
        _, thresh2 = cv2.threshold(roi, 240, 255, cv2.THRESH_BINARY)

        # 找到白色弹窗的轮廓
        contours2, _ = cv2.findContours(thresh2, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours2:
            max_contour2 = max(contours2, key=cv2.contourArea)
            x2, y2, w2, h2 = cv2.boundingRect(max_contour2)
            # 计算在原图中的坐标
            x_final, y_final = x + x2, y + y2
            popup = img[y_final:y_final+h2, x_final:x_final+w2]
            popup_path = image_path.replace('.', '_popup.')
            cv2.imwrite(popup_path, popup)
            return popup_path
        else:
            return image_path

    @staticmethod
    def preprocess_image(image_path: str) -> str:
        """预处理图片，提高OCR识别率"""
        try:
            # 首先尝试检测和裁剪弹窗
            popup_path = ImageProcessor.detect_and_crop_popup(image_path)

            with Image.open(popup_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 如果图片太大，适当缩放
                max_size = 2048
                if img.width > max_size or img.height > max_size:
                    img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

                # 保存处理后的图片
                processed_path = popup_path.replace('.', '_processed.')
                img.save(processed_path, 'JPEG', quality=95)

                return popup_path
        except Exception as e:
            logger.error(f"图片预处理失败: {str(e)}")
            return image_path
