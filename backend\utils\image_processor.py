"""
图片处理工具模块
"""

import os
import logging
from pathlib import Path
from fastapi import UploadFile, HTTPException
from PIL import Image
import cv2
import numpy as np
from config import config

logger = logging.getLogger(__name__)


class ImageProcessor:
    """图片处理类"""
    
    @staticmethod
    def validate_image(file: UploadFile) -> bool:
        """验证上传的图片文件"""
        # 检查文件大小
        if file.size > config.MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="文件大小超过限制")
        
        # 检查文件扩展名
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in config.ALLOWED_EXTENSIONS:
            raise HTTPException(status_code=400, detail="不支持的文件格式")
        
        return True
    
    @staticmethod
    def detect_and_crop_popup(image_path: str) -> str:
        """
        检测并裁剪弹窗区域
        使用OpenCV进行弹窗检测和裁剪
        """
        try:
            # 读取图片
            img = cv2.imread(image_path)
            if img is None:
                logger.warning(f"无法读取图片: {image_path}")
                return image_path

            # 转为灰度图
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            # 二值化（弹窗通常比背景亮）
            _, thresh = cv2.threshold(gray, 220, 255, cv2.THRESH_BINARY)

            # 反色（弹窗区域为白色）
            thresh = 255 - thresh

            # 膨胀操作，连接弹窗区域
            kernel = np.ones((15, 15), np.uint8)
            dilated = cv2.dilate(thresh, kernel, iterations=2)

            # 查找轮廓
            contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                logger.info("未检测到弹窗轮廓，使用原图")
                return image_path

            # 找到最大轮廓（通常就是弹窗）
            max_contour = max(contours, key=cv2.contourArea)
            x, y, w, h = cv2.boundingRect(max_contour)

            # 检查裁剪区域是否合理（避免裁剪过小的区域）
            min_area = img.shape[0] * img.shape[1] * 0.1  # 至少占原图10%
            if w * h < min_area:
                logger.info("检测到的弹窗区域过小，使用原图")
                return image_path

            # 裁剪弹窗
            popup = img[y:y+h, x:x+w]

            # 保存裁剪后的图片
            popup_path = image_path.replace('.', '_popup.')
            cv2.imwrite(popup_path, popup)

            logger.info(f"成功检测并裁剪弹窗: {popup_path}, 区域: ({x},{y},{w},{h})")
            return popup_path

        except Exception as e:
            logger.error(f"弹窗检测失败: {str(e)}")
            return image_path

    @staticmethod
    def preprocess_image(image_path: str) -> str:
        """预处理图片，提高OCR识别率"""
        try:
            # 首先尝试检测和裁剪弹窗
            popup_path = ImageProcessor.detect_and_crop_popup(image_path)

            with Image.open(popup_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')

                # 如果图片太大，适当缩放
                max_size = 2048
                if img.width > max_size or img.height > max_size:
                    img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)

                # 保存处理后的图片
                processed_path = popup_path.replace('.', '_processed.')
                img.save(processed_path, 'JPEG', quality=95)

                return processed_path
        except Exception as e:
            logger.error(f"图片预处理失败: {str(e)}")
            return image_path
