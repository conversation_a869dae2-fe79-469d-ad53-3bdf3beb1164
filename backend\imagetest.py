import cv2
import numpy as np
from PIL import Image

img = cv2.imread('screenshot.png')
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

# 第一次阈值，分离弹窗+阴影
_, thresh1 = cv2.threshold(gray, 200, 255, cv2.THRESH_BINARY)

# 找最大轮廓（弹窗+阴影）
contours, _ = cv2.findContours(255-thresh1, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
max_contour = max(contours, key=cv2.contourArea)
x, y, w, h = cv2.boundingRect(max_contour)

# 在弹窗区域内，二次阈值，提取真正的白色弹窗
roi = gray[y:y+h, x:x+w]
_, thresh2 = cv2.threshold(roi, 240, 255, cv2.THRESH_BINARY)

# 找到白色弹窗的轮廓
contours2, _ = cv2.findContours(thresh2, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
if contours2:
    max_contour2 = max(contours2, key=cv2.contourArea)
    x2, y2, w2, h2 = cv2.boundingRect(max_contour2)
    # 计算在原图中的坐标
    x_final, y_final = x + x2, y + y2
    popup = img[y_final:y_final+h2, x_final:x_final+w2]
    cv2.imwrite('popup_only.png', popup)
    Image.open('popup_only.png').show()
else:
    print("未检测到弹窗内部区域，请调整阈值。")