"""
图片处理相关路由
"""

import os
import tempfile
import logging
from pathlib import Path
from fastapi import APIRouter, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse

from services import OCRService, RAGFlowService
from utils import ImageProcessor
import re

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/image", tags=["图片处理"])


def process_ocr_text(ocr_text: str) -> dict:
    """
    处理OCR文本，提取表单信息
    1. 删除"申请部门"之前的内容
    2. 删除字符*
    3. 提取键值对信息
    """
    # 预定义的表单字段
    form_fields = {
        "申请部门": "",
        "申请日期": "",
        "申请人": "",
        "申请人联系电话": "",
        "目的运行环境": "",
        "源类型": "",
        "有效期": "",
        "至": "",
        "源地址": "",
        "源端口": "",
        "目的地址": "",
        "目的端口": "",
        "端口类型": "",
        "源地址描述": "",
        "目的地址描述": "",
        "备注": ""
    }

    try:
        # 1. 删除"申请部门"之前的内容
        dept_index = ocr_text.find("申请部门")
        if dept_index != -1:
            ocr_text = ocr_text[dept_index:]

        # 2. 删除字符*
        ocr_text = ocr_text.replace("*", "")

        # 3. 清理特殊字符
        ocr_text = ocr_text.replace("α", "").replace("\n\n", "\n")

        # 4. 提取键值对
        # 使用正则表达式匹配 "键：" 后面的内容
        for field_name in form_fields.keys():
            # 构建正则表达式：匹配字段名后的内容，直到下一个字段或文本结束
            pattern = rf"{re.escape(field_name)}：\s*([^：]*?)(?=\n[^：\n]*：|\n*$)"
            match = re.search(pattern, ocr_text, re.DOTALL)

            if match:
                value = match.group(1).strip()
                # 清理值中的多余换行和空格
                value = re.sub(r'\n+', ' ', value).strip()
                form_fields[field_name] = value

        print(f"📋 提取到的表单信息:")
        for key, value in form_fields.items():
            if value:  # 只打印有值的字段
                print(f"  {key}: {value}")

        return form_fields

    except Exception as e:
        logger.error(f"处理OCR文本失败: {str(e)}")
        return form_fields


@router.post("/parse")
async def parse_image(image: UploadFile = File(...)):
    """
    页面截图识别请求
    
    接收前端上传的页面截图图片，识别并提取其中的键值对信息
    """
    try:
        # 验证上传的文件
        ImageProcessor.validate_image(image)
        
        # 保存上传的文件到backend目录下的临时目录
        import uuid
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)  # 创建temp目录

        # 生成唯一的临时文件名
        file_extension = Path(image.filename).suffix or '.png'
        tmp_filename = f"upload_{uuid.uuid4().hex[:8]}{file_extension}"
        tmp_file_path = temp_dir / tmp_filename

        # 保存上传的文件
        content = await image.read()
        with open(tmp_file_path, 'wb') as f:
            f.write(content)

        tmp_file_path = str(tmp_file_path)
        
        try:
            # 预处理图片
            processed_image_path = ImageProcessor.preprocess_image(tmp_file_path)
            print(f"📁 原始图片路径: {tmp_file_path}")
            print(f"✂️ 处理后图片路径: {processed_image_path}")
            print(f"🔍 OCR将使用图片: {processed_image_path}")

            logger.info(f"原始图片路径: {tmp_file_path}")
            logger.info(f"处理后图片路径: {processed_image_path}")

            # 调用OCR识别
            logger.info("开始OCR识别...")
            ocr_text = OCRService.call_umi_ocr(processed_image_path)
            logger.info(f"OCR识别完成，识别文本长度: {len(ocr_text)}")

            print(f"📝 原始OCR文本:")
            print(f"'{ocr_text}'")

            # 处理OCR文本，提取表单信息
            form_data = process_ocr_text(ocr_text)

            # 使用RAGFlow智能体处理OCR结果
            # logger.info("开始RAGFlow智能体处理...")
            # key_value_pairs = RAGFlowService.process_ocr_text(ocr_text)
            # print(key_value_pairs)
            # logger.info(f"智能体处理完成，提取到 {len(key_value_pairs)} 个键值对")
            
            return JSONResponse(content={
                "code": 200,
                "data": {
                    "raw_text": ocr_text,
                    "form_data": form_data
                },
                "msg": "图片识别成功"
            })
            
        finally:
            # pass
            # 清理原始临时文件
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)
            # 暂时保留处理后的图片以便检查
            if 'processed_image_path' in locals():
                print(f"🖼️ 处理后的图片已保存在: {processed_image_path}")
                print(f"📍 完整路径: {os.path.abspath(processed_image_path)}")
                os.unlink(processed_image_path)  # 暂时注释掉，保留图片
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图片识别处理异常: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "data": [],
                "msg": f"图片识别失败: {str(e)}"
            }
        )
