"""
图片处理相关路由
"""

import os
import tempfile
import logging
from pathlib import Path
from fastapi import APIRouter, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse

from services import OCRService, RAGFlowService
from utils import ImageProcessor

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/image", tags=["图片处理"])


@router.post("/parse")
async def parse_image(image: UploadFile = File(...)):
    """
    页面截图识别请求
    
    接收前端上传的页面截图图片，识别并提取其中的键值对信息
    """
    try:
        # 验证上传的文件
        ImageProcessor.validate_image(image)
        
        # 保存上传的文件到临时目录
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(image.filename).suffix) as tmp_file:
            content = await image.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        try:
            # 预处理图片
            processed_image_path = ImageProcessor.preprocess_image(tmp_file_path)
            
            # 调用OCR识别
            logger.info("开始OCR识别...")
            ocr_text = OCRService.call_umi_ocr(processed_image_path)
            logger.info(f"OCR识别完成，识别文本长度: {len(ocr_text)}")
            
            # 使用RAGFlow智能体处理OCR结果
            logger.info("开始RAGFlow智能体处理...")
            key_value_pairs = RAGFlowService.process_ocr_text(ocr_text)
            print(key_value_pairs)
            logger.info(f"智能体处理完成，提取到 {len(key_value_pairs)} 个键值对")
            
            return JSONResponse(content={
                "code": 200,
                "data": key_value_pairs,
                "msg": "图片识别成功"
            })
            
        finally:
            # 清理临时文件
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)
            if 'processed_image_path' in locals() and os.path.exists(processed_image_path):
                os.unlink(processed_image_path)
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图片识别处理异常: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "data": [],
                "msg": f"图片识别失败: {str(e)}"
            }
        )
