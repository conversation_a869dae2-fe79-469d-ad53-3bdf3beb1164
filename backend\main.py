from fastapi import FastAPI, File, UploadFile, HTTPException,Query
from fastapi.responses import JSONResponse
from typing import List, Dict, Any
import base64
import requests
import json
import logging
from pathlib import Path
import tempfile
import os
from PIL import Image
import io
from ragflow_sdk import RAGFlow, Agent
from pydantic import BaseModel, ValidationError
from typing import List, Dict, Optional
import json
import re

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="页面识别请求API", version="1.0.0")

# 配置项
class Config:
    # UMI-OCR 配置
    UMI_OCR_API_URL = "http://localhost:1224/api/ocr"  # UMI-OCR API地址
    
    # RAGFlow 配置
    RAGFLOW_API_KEY = "ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm"  # RAGFlow API密钥
    RAGFLOW_BASE_URL = "http://*************:8011"  # RAGFlow API地址
    RAGFLOW_ASSISTANT_ID = "00c390745ed211f0bb210242ac150006"  # RAGFlow 智能体ID
    
    # 文件配置
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    ALLOWED_EXTENSIONS = {'.png', '.jpg', '.jpeg', '.bmp', '.gif', '.tiff'}

config = Config()

# 初始化 RAGFlow 客户端
rag_object = RAGFlow(api_key=config.RAGFLOW_API_KEY, base_url=config.RAGFLOW_BASE_URL)

class OCRService:
    """OCR服务类"""
    
    @staticmethod
    def call_umi_ocr(image_path: str) -> str:
        """调用UMI-OCR进行图片识别"""
        try:
            # 将图片转换为base64
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            # 调用UMI-OCR API
            payload = {
                "base64": image_data,
                "options": {
                    "language": ["chinese", "english"],  # 支持中英文
                    "cls": True,  # 启用方向分类
                    "det": True,  # 启用文本检测
                    "rec": True ,  # 启用文本识别
                    "tbpu.parser":"single_para"
                }
            }
            
            response = requests.post(
                config.UMI_OCR_API_URL,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 100:
                    # 提取识别的文本
                    text_list = []
                    for item in result.get("data", []):
                        text_list.append(item.get("text", ""))
                    raw_text = "\n".join(text_list)
                    
                    # 处理文本：删除"设备安装照片"及其后面的所有文本
                    processed_text = OCRService.filter_installation_photos(raw_text)
                    
                    return processed_text
                else:
                    raise Exception(f"OCR识别失败: {result.get('message', 'Unknown error')}")
            else:
                raise Exception(f"OCR API请求失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"OCR识别异常: {str(e)}")
            raise
    
    @staticmethod
    def filter_installation_photos(text: str) -> str:
        """
        过滤文本中的设备安装照片部分
        如果文本中包含"设备安装照片"，则删除该关键词及其后面的所有文本
        """
        if not text:
            return text
            
        # 查找"设备安装照片"的位置
        keyword = "设备安装照片"
        keyword_index = text.find(keyword)
        
        if keyword_index != -1:
            # 如果找到关键词，则截取关键词之前的文本
            filtered_text = text[:keyword_index].strip()
            logger.info(f"检测到关键词'{keyword}'，已删除该关键词及其后面的文本")
            logger.info(f"原文本长度: {len(text)}, 过滤后文本长度: {len(filtered_text)}")
            return filtered_text
        else:
            # 如果没有找到关键词，返回原文本
            return text

class RAGFlowService:
    """RAGFlow智能体服务类"""
    
    @staticmethod
    def process_ocr_text(ocr_text: str) -> List[Dict[str, str]]:
        """使用RAGFlow智能体处理OCR文本，提取键值对"""
        try:
            # 构造提示词
            prompt = f"""

            OCR文本：
            {ocr_text}
            
        
            """
            logger.info(f"【智能体输入内容】:\n{ocr_text}")
            # 使用RAGFlow SDK
            try:
                # 获取指定的智能体
                agent = rag_object.list_agents(id=config.RAGFLOW_ASSISTANT_ID)[0]
                # logger.info(f"成功获取智能体: {agent.name('name', 'Unknown')}")
                
                # 创建会话
                session = agent.create_session()
                logger.info("成功创建会话")
                
                # 发送消息并获取响应
                response_content = ""
                logger.info("开始发送消息到智能体...")
                
                for ans in session.ask(prompt, stream=True):
                    response_content = ans.content
                
                logger.info(f"智能体响应完成，内容长度: {len(response_content)}")

                # 尝试解析JSON
                try:
                    import re
                    import json

                    # 查找JSON对象（而不是数组）
                    json_match = re.search(r'\{.*?\}', response_content, re.DOTALL)
                    if json_match:
                        json_str = json_match.group()
                        key_value_pairs = json.loads(json_str)
                        logger.info(f"成功解析JSON，提取到 {len(key_value_pairs)} 个键值对")
                        return key_value_pairs
                    else:
                        logger.warning("未找到JSON格式，使用备用解析方法")
                        return RAGFlowService.parse_simple_format(response_content)

                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析失败: {str(e)}，使用备用解析方法")
                    return RAGFlowService.parse_simple_format(response_content)


            except Exception as e:
                logger.error(f"RAGFlow SDK调用异常: {str(e)}")
                raise
                
        except Exception as e:
            logger.error(f"RAGFlow处理异常: {str(e)}")
            # 如果RAGFlow处理失败，使用备用方法
            return RAGFlowService.parse_simple_format(ocr_text)
    
    @staticmethod
    def parse_simple_format(text: str) -> List[Dict[str, str]]:
        """简单的文本解析方法，作为备用方案"""
        key_value_pairs = []
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 尝试多种分隔符
            separators = ['：', ':', '=', '|', '\t']
            for sep in separators:
                if sep in line:
                    parts = line.split(sep, 1)
                    if len(parts) == 2:
                        key = parts[0].strip()
                        value = parts[1].strip()
                        if key and value:
                            key_value_pairs.append({
                                "key": key,
                                "value": value
                            })
                    break
        
        return key_value_pairs

class ImageProcessor:
    """图片处理类"""
    
    @staticmethod
    def validate_image(file: UploadFile) -> bool:
        """验证上传的图片文件"""
        # 检查文件大小
        if file.size > config.MAX_FILE_SIZE:
            raise HTTPException(status_code=400, detail="文件大小超过限制")
        
        # 检查文件扩展名
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in config.ALLOWED_EXTENSIONS:
            raise HTTPException(status_code=400, detail="不支持的文件格式")
        
        return True
    
    @staticmethod
    def preprocess_image(image_path: str) -> str:
        """预处理图片，提高OCR识别率"""
        try:
            with Image.open(image_path) as img:
                # 转换为RGB模式
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # 如果图片太大，适当缩放
                max_size = 2048
                if img.width > max_size or img.height > max_size:
                    img.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
                
                # 保存处理后的图片
                processed_path = image_path.replace('.', '_processed.')
                img.save(processed_path, 'JPEG', quality=95)
                
                return processed_path
        except Exception as e:
            logger.error(f"图片预处理失败: {str(e)}")
            return image_path

@app.post("/image/parse")
async def parse_image(image: UploadFile = File(...)):
    """
    页面截图识别请求
    
    接收前端上传的页面截图图片，识别并提取其中的键值对信息
    """
    try:
        # 验证上传的文件
        ImageProcessor.validate_image(image)
        
        # 保存上传的文件到临时目录
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(image.filename).suffix) as tmp_file:
            content = await image.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        try:
            # 预处理图片
            processed_image_path = ImageProcessor.preprocess_image(tmp_file_path)
            
            # 调用OCR识别
            logger.info("开始OCR识别...")
            ocr_text = OCRService.call_umi_ocr(processed_image_path)
            logger.info(f"OCR识别完成，识别文本长度: {len(ocr_text)}")
            
            # 使用RAGFlow智能体处理OCR结果
            logger.info("开始RAGFlow智能体处理...")
            key_value_pairs = RAGFlowService.process_ocr_text(ocr_text)
            print(key_value_pairs)
            logger.info(f"智能体处理完成，提取到 {len(key_value_pairs)} 个键值对")
            
            return JSONResponse(content={
                "code": 200,
                "data": key_value_pairs,
                "msg": "图片识别成功"
            })
            
        finally:
            # 清理临时文件
            if os.path.exists(tmp_file_path):
                os.unlink(tmp_file_path)
            if 'processed_image_path' in locals() and os.path.exists(processed_image_path):
                os.unlink(processed_image_path)
                
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"图片识别处理异常: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "data": [],
                "msg": f"图片识别失败: {str(e)}"
            }
        )

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "message": "服务正常运行"}

# 新增：测试RAGFlow连接的接口
@app.get("/ragflow/test")
async def test_ragflow_connection():
    """测试RAGFlow连接"""
    try:
        agents = rag_object.list_agents()
        target_agent = None
        for agent in agents:
            if agent.id == config.RAGFLOW_ASSISTANT_ID:
                target_agent = agent
                break
        
        if target_agent:
            return JSONResponse(content={
                "code": 200,
                "data": {
                    "agent_id": target_agent.id,
                    "agent_name": target_agent.name,
                    "status": "connected"
                },
                "msg": "RAGFlow连接成功"
            })
        else:
            return JSONResponse(content={
                "code": 404,
                "data": None,
                "msg": f"未找到指定的智能体ID: {config.RAGFLOW_ASSISTANT_ID}"
            })
            
    except Exception as e:
        logger.error(f"RAGFlow连接测试失败: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "data": None,
                "msg": f"RAGFlow连接失败: {str(e)}"
            }
        )


class KeyValueItem(BaseModel):
    """请求体键值对模型"""
    key: str
    value: str


class FormRequest(BaseModel):
    """请求体模型定义"""
    items: List[KeyValueItem]  # 必须包含的键值对列表


class ValidationResponse(BaseModel):
    """响应模型"""
    code: int  # 状态码（200=成功, 400=参数错误, 500=服务器错误）
    data: Optional[str] = None  # 修改建议（校验失败时返回）
    msg: Optional[str] = None  # 提示信息


def validate_form_data(items: List[KeyValueItem]) -> Dict:
    """
    表单校验逻辑
    支持以下校验规则：
    1. 年龄必须为数字
    2. 邮箱必须符合格式
    3. 手机号必须为11位数字
    4. 必填字段非空校验
    """
    from ragflow_sdk import RAGFlow, Agent

    rag_object = RAGFlow(api_key="ragflow-NhYWY1MGU0NDFiMjExZjBiZTc5MDI0Mm", base_url="http://*************:8011")
    AGENT_id = "815cced04b1811f0bfdf0242ac150006"
    agent = rag_object.list_agents(id=AGENT_id)[0]
    session = agent.create_session()

    print("\n==============\n")
    print("Hello. What can I do for you?")

    while True:
        # question = input("\n===== User ====\n> ")
        question = str(items)
        print(items)
        print("\n==== Content checking in progress ====\n")

        suggestion = []
        cont = ""
        for ans in session.ask(question, stream=True):
            print(ans.content[len(cont):], end='', flush=True)
            cont = ans.content
        suggestion.append(cont)
        print("\n==================\n")
        return {"suggestions": suggestion}


@app.post("/form/validate", response_model=ValidationResponse)
async def handle_form_validation(form_data: FormRequest):
    """
    表单校验接口
    - 自动验证请求体格式
    - 执行多维度数据校验
    - 返回结构化校验结果
    """
    try:
        # 执行校验逻辑
        validation_result = validate_form_data(form_data.items)
        suggestions = validation_result["suggestions"]

        # 构建响应
        if suggestions:
            return {
                "code": 400,
                "data": "；".join(suggestions),
                "msg": "存在需要修改的字段"
            }
        else:
            return {
                "code": 200,
                "data": None,
                "msg": "所有字段校验通过"
            }

    except ValidationError as e:
        raise HTTPException(status_code=400, detail=f"请求格式错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@app.get("/form/validate", response_model=str)
async def get_validation_result(
        items_str: str = Query(
            ...,
            description="URL编码的JSON字符串，格式示例：[{\"key\":\"年龄\",\"value\":\"25\"}]"
        )
):
    """
    通过GET请求获取表单校验结果
    - 参数需进行URL编码
    - 直接返回校验建议字符串
    """
    try:
        # 1. URL解码
        import urllib.parse
        decoded_str = urllib.parse.unquote(items_str)

        # 2. 解析JSON字符串
        items = json.loads(decoded_str)

        # 3. 转换为Pydantic模型
        form_items = [KeyValueItem(key=item["key"], value=item["value"]) for item in items]

        # 4. 执行校验逻辑
        validation_result = validate_form_data(form_items)
        suggestions = validation_result.get("suggestions", [])

        # 5. 构建响应字符串
        return "；".join(suggestions) if suggestions else "所有字段校验通过"

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="JSON格式解析失败")
    except KeyError as e:
        raise HTTPException(status_code=400, detail=f"缺失必要字段: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器处理异常: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)