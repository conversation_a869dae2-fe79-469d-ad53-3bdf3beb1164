"""
FastAPI主应用程序
重构后的简化版本，只负责应用初始化和路由注册
"""

import logging
from fastapi import FastAPI

# 导入路由模块
from routers import image_router, form_router, health_router

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(title="页面识别请求API", version="1.0.0")

# 注册路由
app.include_router(image_router)
app.include_router(form_router)
app.include_router(health_router)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)