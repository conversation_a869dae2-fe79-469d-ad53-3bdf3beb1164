"""
FastAPI主应用程序
重构后的简化版本，只负责应用初始化和路由注册
"""

import logging
from fastapi import FastAPI

# 导入路由模块
from routers import image_router, form_router, health_router

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # 输出到控制台
        logging.FileHandler('app.log', encoding='utf-8')  # 输出到文件
    ]
)
logger = logging.getLogger(__name__)

# 创建FastAPI应用实例
app = FastAPI(title="页面识别请求API", version="1.0.0")

# 注册路由
app.include_router(image_router)
app.include_router(form_router)
app.include_router(health_router)


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)