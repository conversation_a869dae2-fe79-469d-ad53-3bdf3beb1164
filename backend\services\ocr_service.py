"""
OCR服务模块
处理图片文字识别相关功能
"""

import base64
import requests
import logging
from config import config

logger = logging.getLogger(__name__)


class OCRService:
    """OCR服务类"""
    
    @staticmethod
    def call_umi_ocr(image_path: str) -> str:
        """调用UMI-OCR进行图片识别"""
        try:
            # 将图片转换为base64
            with open(image_path, 'rb') as f:
                image_data = base64.b64encode(f.read()).decode('utf-8')
            
            # 调用UMI-OCR API
            payload = {
                "base64": image_data,
                "options": {
                    "language": ["chinese", "english"],  # 支持中英文
                    "cls": True,  # 启用方向分类
                    "det": True,  # 启用文本检测
                    "rec": True,  # 启用文本识别
                    "tbpu.parser": "single_para"
                }
            }
            
            response = requests.post(
                config.UMI_OCR_API_URL,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 100:
                    # 提取识别的文本
                    text_list = []
                    for item in result.get("data", []):
                        text_list.append(item.get("text", ""))
                    raw_text = "\n".join(text_list)
                    
                    # 处理文本：删除"设备安装照片"及其后面的所有文本
                    processed_text = OCRService.filter_installation_photos(raw_text)
                    
                    return processed_text
                else:
                    raise Exception(f"OCR识别失败: {result.get('message', 'Unknown error')}")
            else:
                raise Exception(f"OCR API请求失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"OCR识别异常: {str(e)}")
            raise
    
    @staticmethod
    def filter_installation_photos(text: str) -> str:
        """
        过滤文本中的设备安装照片部分
        如果文本中包含"设备安装照片"，则删除该关键词及其后面的所有文本
        """
        if not text:
            return text
            
        # 查找"设备安装照片"的位置
        keyword = "设备安装照片"
        keyword_index = text.find(keyword)
        
        if keyword_index != -1:
            # 如果找到关键词，则截取关键词之前的文本
            filtered_text = text[:keyword_index].strip()
            logger.info(f"检测到关键词'{keyword}'，已删除该关键词及其后面的文本")
            logger.info(f"原文本长度: {len(text)}, 过滤后文本长度: {len(filtered_text)}")
            return filtered_text
        else:
            # 如果没有找到关键词，返回原文本
            return text
