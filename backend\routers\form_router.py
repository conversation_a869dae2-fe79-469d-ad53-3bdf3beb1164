"""
表单处理相关路由
"""

import json
import urllib.parse
import logging
from fastapi import APIRouter, HTTPException, Query
from pydantic import ValidationError

from models import FormRequest, ValidationResponse, KeyValueItem
from utils import validate_form_data

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/form", tags=["表单处理"])


@router.post("/validate", response_model=ValidationResponse)
async def handle_form_validation(form_data: FormRequest):
    """
    表单校验接口
    - 自动验证请求体格式
    - 执行多维度数据校验
    - 返回结构化校验结果
    """
    try:
        # 执行校验逻辑
        validation_result = validate_form_data(form_data.items)
        suggestions = validation_result["suggestions"]

        # 构建响应
        if suggestions:
            return {
                "code": 400,
                "data": "；".join(suggestions),
                "msg": "存在需要修改的字段"
            }
        else:
            return {
                "code": 200,
                "data": None,
                "msg": "所有字段校验通过"
            }

    except ValidationError as e:
        raise HTTPException(status_code=400, detail=f"请求格式错误: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")


@router.get("/validate", response_model=str)
async def get_validation_result(
        items_str: str = Query(
            ...,
            description="URL编码的JSON字符串，格式示例：[{\"key\":\"年龄\",\"value\":\"25\"}]"
        )
):
    """
    通过GET请求获取表单校验结果
    - 参数需进行URL编码
    - 直接返回校验建议字符串
    """
    try:
        # 1. URL解码
        decoded_str = urllib.parse.unquote(items_str)

        # 2. 解析JSON字符串
        items = json.loads(decoded_str)

        # 3. 转换为Pydantic模型
        form_items = [KeyValueItem(key=item["key"], value=item["value"]) for item in items]

        # 4. 执行校验逻辑
        validation_result = validate_form_data(form_items)
        suggestions = validation_result.get("suggestions", [])

        # 5. 构建响应字符串
        return "；".join(suggestions) if suggestions else "所有字段校验通过"

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="JSON格式解析失败")
    except KeyError as e:
        raise HTTPException(status_code=400, detail=f"缺失必要字段: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"服务器处理异常: {str(e)}")
